{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debugger: uvicorn",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "api.main:app",
                "--host",
                "0.0.0.0",
                "--port",
                "8080",
                "--no-server-header",
            ],
            "cwd": "${workspaceFolder}/interviewpro",
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/interviewpro"
            },
            "jinja": true,
            "justMyCode": false
        },
    ]
}