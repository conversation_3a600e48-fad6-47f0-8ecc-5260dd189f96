# InterviewPro API

## Requirements

_Note: fastapi a nd pydantic-core does not support Python 3.14 yet. You can use 3.13 for now._

- `uv` package management: https://docs.astral.sh/uv/getting-started/installation/

## Local development

1. Install Python

    ```bash
    uv install python3.13
    ```

2. Init project

    ```bash
    uv init
    ```

3. Add packages

    ```bash
    uv add fastapi
    ```

## Docker

### Building the Docker image

```bash
docker build -t interviewpro-api .
```

### Running the container

```bash
# Run in foreground
docker run --rm -p 8080:8080 interviewpro-api

# Run in background
docker run -d -p 8080:8080 --name interviewpro-api interviewpro-api
```

### Environment Variables

The following environment variables can be configured:

**Server Configuration:**
- `HOST`: Server host (default: `0.0.0.0`)
- `PORT`: Server port (default: `8080`)
- `WORKERS`: Number of worker processes (default: `1`)

**Application Settings:**
- `APP_NAME`: Application name (default: `InterviewPro`)
- `APP_ENV`: Environment (default: `development`)
- `APP_DEBUG`: Debug mode (default: `false`)

**Advanced Uvicorn Options:**
- `RELOAD`: Enable auto-reload for development (default: `false`)
- `LOG_LEVEL`: Log level (`debug`, `info`, `warning`, `error`, `critical`)
- `ACCESS_LOG`: Enable access logs (default: `true`, set to `false` to disable)

Example with custom environment variables:

```bash
# Production setup with multiple workers
docker run --rm -p 9000:9000 \
  -e HOST=0.0.0.0 \
  -e PORT=9000 \
  -e WORKERS=4 \
  -e APP_ENV=production \
  -e APP_DEBUG=false \
  -e LOG_LEVEL=info \
  interviewpro-api

# Development setup with reload
docker run --rm -p 8080:8080 \
  -e RELOAD=true \
  -e LOG_LEVEL=debug \
  -e APP_DEBUG=true \
  interviewpro-api
```

### Health Checks

The container includes health checks for:

- **Liveness**: `GET /system/liveness`
- **Readiness**: `GET /system/readiness`

### Docker Compose

You can also use the existing `docker-compose.yaml` to run the full stack including PostgreSQL and Redis.
