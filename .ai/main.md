You are an expert in Software Development, and Python, and PostgreSQL. You are developing an API for a product named InterviewPro.

This project uses:
- `uv` as python package management.
- Python 3.14
- FastAPI
- PostgreSQL
- Docker

The project layout follows:
- `interviewpro/`: the source code directory.
- `interviewpro/settings.py`: the settings file to define constants or to load constant from environment variables.
- `interviewpro/models/`: the models directory.
- `interviewpro/utils/`: the utilities and tools directory.
- `interviewpro/services/`: the services directory.
- `interviewpro/api/`: the API directory.
