"""System-related API endpoints."""
from fastapi import FastAPI
from fastapi.responses import JSONResponse

healthcheck_app = FastAPI()


@healthcheck_app.get("/liveness", tags=["System"])
async def liveness_check():
    """Liveness check endpoint."""
    return JSONResponse(content={"status": "ok"}, status_code=200)


@healthcheck_app.get("/readiness", tags=["System"])
async def readiness_check():
    """Readiness check endpoint."""
    return JSONResponse(content={"status": "ok"}, status_code=200)
