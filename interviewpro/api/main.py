"""Main API application."""
from fastapi import FastAPI
from settings import APP_HOST, APP_PORT, APP_NAME, APP_DEBUG, LOGGING_CONFIG
from api.healthcheck import healthcheck_app


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    _app = FastAPI(
        title=APP_NAME,
        version="1.0.0",
        debug=APP_DEBUG,
    )

    _app.mount("/healthcheck", app=healthcheck_app)

    return _app


# Create app instance for uvicorn
app = create_app()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host=APP_HOST, port=APP_PORT, log_config=LOGGING_CONFIG)
