[project]
name = "interviewpro-api"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aerich>=0.9.1",
    "fastapi>=0.117.1",
    "tortoise-orm[asyncpg,psycopg]>=0.25.1",
    "uvicorn[standard]>=0.36.0",
]

[dependency-groups]
dev = [
    "pyclean>=3.1.0",
]

[tool.aerich]
tortoise_orm = "interviewpro.settings.TORTOISE_ORM_CONFIG"
location = "./migrations"
src_folder = "./."
